"""
LLM Vision System ana çalıştırma modülü
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Proje kök dizinini sys.path'e ekle
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from llm_vision.server import MCPServer
from llm_vision.config import config
from llm_vision.utils.logger import get_logger

logger = get_logger(__name__)


def create_parser() -> argparse.ArgumentParser:
    """Komut satırı argüman parser'ı oluştur"""
    parser = argparse.ArgumentParser(
        description="LLM Vision System - MCP Sunucusu",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Örnekler:
  python -m llm_vision                    # Varsayılan ayarlarla başlat
  python -m llm_vision --host 0.0.0.0    # Tüm arayüzlerde dinle
  python -m llm_vision --port 8080       # Farklı port kullan
  python -m llm_vision --debug           # Debug modunda çalıştır
        """
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default=config.server.host,
        help=f"Sunucu host adresi (varsayılan: {config.server.host})"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=config.server.port,
        help=f"Sunucu port numarası (varsayılan: {config.server.port})"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Debug modunda çalıştır"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version=f"LLM Vision System {config.mcp.server_version}"
    )
    
    return parser


async def main() -> None:
    """Ana fonksiyon"""
    parser = create_parser()
    args = parser.parse_args()
    
    # Debug modunu ayarla
    if args.debug:
        config.server.debug = True
    
    logger.info("LLM Vision System başlatılıyor...")
    logger.info(f"Sunucu: {args.host}:{args.port}")
    logger.info(f"Debug modu: {config.server.debug}")
    
    try:
        # MCP sunucusunu oluştur ve başlat
        server = MCPServer()
        await server.start(host=args.host, port=args.port)
        
    except KeyboardInterrupt:
        logger.info("Sunucu kapatılıyor...")
    except Exception as e:
        logger.error(f"Sunucu hatası: {e}")
        sys.exit(1)


def run() -> None:
    """Senkron çalıştırma fonksiyonu"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Program sonlandırıldı")
    except Exception as e:
        logger.error(f"Program hatası: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run()
